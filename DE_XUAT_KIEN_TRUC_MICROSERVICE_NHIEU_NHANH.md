# 🏗️ Đề xuất Kiến trúc Microservice Đa Nhánh - <PERSON>ệ thống QL_CTDT

## 📋 Tóm tắt Điều hành

**Đề xuất**: Tái cấu trúc hệ thống QL_CTDT thành kiến trúc microservice Hub-and-Spoke với ba nhánh chuyên biệt:
- **Nhánh Main**: Trung tâm quản lý, phân tích và cổng người dùng
- **Nhánh Dev**: Dịch vụ đánh giá nhẹ, chỉ tập trung vào thi
- **Nh<PERSON>h DevGame**: Môi trường luyện tập với đầy đủ tính năng gamification

**Mục tiêu**: Tối ưu hóa tài nguyên, cải thiện hiệu suất và chuẩn bị cho việc mở rộng microservice trong tương lai, đồng thời duy trì trải nghiệm người dùng mượt mà.

## 🎯 Yêu cầu <PERSON> doanh & <PERSON>ý do

### Thách thức Hiện tại
- **Hạn chế Tài nguyên**: Giới hạn server 8GB RAM
- **Tính năng Thừa**: Nhân đôi full stack trên các nhánh
- **Chi phí Bảo trì**: Code và dependencies trùng lặp
- **Khó Mở rộng**: Hạn chế của kiến trúc monolithic

### Lợi ích Giải pháp Đề xuất
- **Tối ưu Tài nguyên**: Giảm 30-40% RAM (7.5GB → 5-6GB)
- **Cải thiện Hiệu suất**: Thời gian tải nhanh hơn và phản hồi tốt hơn
- **Sẵn sàng Microservice**: Tách biệt rõ ràng cho việc mở rộng tương lai
- **Hiệu quả Bảo trì**: Trách nhiệm đơn lẻ cho mỗi dịch vụ

## 🏗️ Tổng quan Kiến trúc

### Thiết kế Tổng thể
```
┌─────────────────────────────────────────────────────────┐
│                  NHÁNH MAIN (HUB)                      │
│                 (localhost:3000)                       │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │ Cổng Giáo viên  │  │ Cổng Sinh viên  │              │
│  │ • Tạo Quiz      │  │ • Nhập PIN      │              │
│  │ • Chọn Chế độ   │  │ • Xem Kết quả   │              │
│  │ • Theo dõi Live │  │ • Lịch sử       │              │
│  │ • Phân tích     │  │ • Hồ sơ         │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────┬─────────────────┬─────────────────────┘
                  │                 │
        ┌─────────▼─────────┐ ┌─────▼─────────────┐
        │   DỊCH VỤ DEV     │ │ DỊCH VỤ DEVGAME   │
        │ (Chỉ Đánh giá)    │ │ (Luyện tập + Game)│
        │  localhost:3001   │ │  localhost:3002   │
        │                   │ │                   │
        │ TÍNH NĂNG TỐI ƯU: │ │ TÍNH NĂNG ĐẦY ĐỦ: │
        │ • Công cụ Quiz    │ │ • Công cụ Quiz    │
        │ • Hệ thống Timer  │ │ • Hệ thống Timer  │
        │ • Nộp Kết quả    │ │ • Gamification    │
        │ • Giao diện Cơ bản│ │ • Hệ thống Tiền tệ│
        │                   │ │ • Hệ thống Kỹ năng│
        └───────────────────┘ └───────────────────┘
                  │                 │
        ┌─────────▼─────────────────▼─────────┐
        │         Tài nguyên Chia sẻ         │
        │ • Cơ sở dữ liệu PostgreSQL         │
        │ • Redis Session Store              │
        │ • Xác thực Chia sẻ                 │
        └─────────────────────────────────────┘
```

### Luồng Người dùng
```
LUỒNG GIÁO VIÊN:
1. Đăng nhập → Dashboard Main
2. Tạo Quiz → Chọn Chế độ (Đánh giá/Luyện tập)
3. Tạo PIN → Quiz được triển khai đến nhánh đích
4. Chia sẻ PIN → Sinh viên truy cập qua cổng main
5. Theo dõi Live → Tổng hợp kết quả thời gian thực
6. Xem Phân tích → Báo cáo toàn diện

LUỒNG SINH VIÊN:
1. Truy cập Cổng Main → Nhập PIN
2. Tự động Chuyển hướng → Đến nhánh phù hợp (dev/devgame)
3. Hoàn thành Quiz → Trong môi trường chuyên biệt
4. Tự động Quay lại → Cổng main để xem kết quả
5. Xem Phản hồi → Giao diện kết quả thống nhất
```

## 🗄️ Kiến trúc Cơ sở dữ liệu

### Chiến lược Cơ sở dữ liệu Chia sẻ
Sử dụng một cơ sở dữ liệu PostgreSQL duy nhất với các bảng được thiết kế để phục vụ tất cả các dịch vụ:

**Bảng Quản lý Quiz Cốt lõi**:
- `quizzes`: Thông tin quiz với chế độ và nhánh đích
- `quiz_sessions`: Theo dõi phiên làm bài qua các nhánh
- `quiz_results`: Kết quả thống nhất cho phân tích
- `users`: Thông tin người dùng với nhận dạng nhánh

**Chiến lược Cách ly Dữ liệu**:
- **Bảng Chia sẻ**: Users, Quizzes, Sessions, Results
- **Bảng Riêng theo Nhánh**: Currency (devgame), Proctoring (dev)
- **Lọc Dữ liệu**: Tất cả truy vấn bao gồm bộ lọc `branch_mode`
- **Tổng hợp Phân tích**: Báo cáo xuyên nhánh trong main

## 🔧 Đặc tả Dịch vụ

### Dịch vụ Main (Hub)
**Mục đích**: Quản lý trung tâm và cổng người dùng
**Cổng**: 3000
**Phân bổ Tài nguyên**: 2GB RAM

**Tính năng**:
- Dashboard giáo viên và quản lý quiz
- Cổng sinh viên và hệ thống nhập PIN
- Theo dõi thời gian thực và phân tích
- Xác thực người dùng và quản lý phiên
- Điều phối giao tiếp xuyên nhánh

**Công nghệ**:
- Frontend: Next.js với React
- Backend: Express.js với TypeScript
- Database: Sequelize ORM với PostgreSQL
- Realtime: Socket.IO cho cập nhật live
- Session: Redis-based session store
- UI: Tailwind CSS + Shadcn/ui components

### Dịch vụ Dev (Đánh giá)
**Mục đích**: Công cụ quiz nhẹ cho đánh giá chính thức
**Cổng**: 3001
**Phân bổ Tài nguyên**: 800MB RAM

**Tính năng (BỘ TỐI ƯU)**:
- Hiển thị và điều hướng quiz
- Hệ thống timer với thực thi nghiêm ngặt
- Nộp và xác thực câu trả lời
- Tính năng giám sát cơ bản
- Truyền kết quả đến main

**Tính năng Loại trừ**:
- Quản lý người dùng (được xử lý bởi main)
- Dashboard phân tích
- Giao diện tạo quiz
- Các yếu tố gamification
- Hệ thống tiền tệ

**Công nghệ**:
- Frontend: React SPA nhẹ
- Backend: Express.js (tối thiểu)
- Database: Sequelize (chỉ models quiz)
- Dependencies: Chỉ các gói thiết yếu

### Dịch vụ DevGame (Luyện tập)
**Mục đích**: Môi trường luyện tập đầy đủ tính năng với gamification
**Cổng**: 3002
**Phân bổ Tài nguyên**: 1.2GB RAM

**Tính năng**:
- Công cụ quiz với UI/UX nâng cao
- Hệ thống gamification hoàn chỉnh
- Quản lý tiền tệ và kỹ năng
- Hệ thống thành tích
- Phân tích riêng cho luyện tập
- Tính năng xã hội (bảng xếp hạng, v.v.)

**Công nghệ**:
- Frontend: React với components gaming UI
- Backend: Express.js với tính năng gaming
- Database: Sequelize với models gamification
- Realtime: Socket.IO cho tính năng multiplayer
- Gaming: Custom gamification engine

## 📊 Phân tích Hiệu suất & Tài nguyên

### So sánh Sử dụng Tài nguyên Hiện tại vs Đề xuất

**KIẾN TRÚC HIỆN TẠI (Full Stack Mỗi Nhánh)**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│   Dịch vụ   │    RAM      │    CPU      │  Tính năng │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ Main        │    2.0GB    │    40%      │    100%     │
│ Dev         │    1.5GB    │    30%      │    100%     │
│ DevGame     │    1.5GB    │    30%      │    100%     │
│ Database    │    1.5GB    │    20%      │     -       │
│ Redis       │    0.5GB    │    5%       │     -       │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ TỔNG CỘNG   │    7.0GB    │   125%      │ Trùng lặp   │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

**KIẾN TRÚC ĐỀ XUẤT (Chuyên biệt hóa Microservice)**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│   Dịch vụ   │    RAM      │    CPU      │  Tính năng │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ Main Hub    │    2.0GB    │    35%      │ Quản lý     │
│ Dev Micro   │    0.8GB    │    15%      │ Chỉ Quiz    │
│ Game Micro  │    1.2GB    │    25%      │ Quiz+Gaming │
│ Database    │    1.5GB    │    20%      │     -       │
│ Redis       │    0.5GB    │    5%       │     -       │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ TỔNG CỘNG   │    6.0GB    │   100%      │ Tối ưu     │
└─────────────┴─────────────┴─────────────┴─────────────┘

TIẾT KIỆM: 1GB RAM (giảm 14%) + 25% hiệu quả CPU
```

### Dự báo Kiểm tra Tải
```
KHẢ NĂNG NGƯỜI DÙNG ĐỒNG THỜI:
┌─────────────────┬─────────────┬─────────────┬─────────────┐
│    Tình huống   │   Hiện tại  │  Đề xuất    │ Cải thiện   │
├─────────────────┼─────────────┼─────────────┼─────────────┤
│ Làm Quiz        │     200     │     300     │    +50%     │
│ Theo dõi Live   │      50     │     100     │   +100%     │
│ Xem Phân tích   │      30     │      80     │   +167%     │
│ Tải hỗn hợp     │     150     │     250     │    +67%     │
└─────────────────┴─────────────┴─────────────┴─────────────┘
```

## 🚀 Lộ trình Triển khai

### Giai đoạn 1: Thiết lập Nền tảng (Tuần 1-2)
**Mục tiêu**: Thiết lập cơ sở hạ tầng microservice

**Nhiệm vụ**:
- Tạo cấu trúc thư mục dịch vụ
- Thiết lập cấu hình Docker Compose
- Triển khai schema cơ sở dữ liệu chia sẻ
- Cấu hình Redis cho phiên xuyên dịch vụ
- Thiết lập NGINX load balancer
- Triển khai kiểm tra sức khỏe cơ bản

**Kết quả**:
- Môi trường Docker hoạt động
- Scripts migration cơ sở dữ liệu
- Framework giao tiếp dịch vụ
- Thiết lập giám sát cơ bản

### Giai đoạn 2: Phát triển Main Hub (Tuần 3-4)
**Mục tiêu**: Xây dựng cổng quản lý trung tâm

**Nhiệm vụ**:
- Triển khai dashboard giáo viên
- Tạo giao diện quản lý quiz
- Xây dựng cổng sinh viên với nhập PIN
- Thiết lập giám sát thời gian thực
- Triển khai tổng hợp phân tích
- Tạo service gateway cho giao tiếp

**Kết quả**:
- Ứng dụng dịch vụ main hoàn chỉnh
- Giao diện giáo viên và sinh viên
- Hệ thống triển khai quiz
- Dashboard thời gian thực
- Báo cáo phân tích

### Giai đoạn 3: Dịch vụ Dev (Đánh giá) (Tuần 5)
**Mục tiêu**: Xây dựng dịch vụ đánh giá nhẹ

**Nhiệm vụ**:
- Tạo công cụ quiz tối thiểu
- Triển khai hệ thống timer
- Xây dựng nộp kết quả
- Thiết lập tính năng giám sát
- Tối ưu hóa hiệu suất
- Triển khai tự động chuyển hướng đến main

**Kết quả**:
- Dịch vụ dev nhẹ
- Giao diện làm quiz
- Tính năng riêng cho đánh giá
- Tối ưu hóa hiệu suất

### Giai đoạn 4: Dịch vụ DevGame (Luyện tập) (Tuần 6-7)
**Mục tiêu**: Xây dựng dịch vụ luyện tập đầy đủ tính năng

**Nhiệm vụ**:
- Port các tính năng gamification hiện có
- Triển khai hệ thống tiền tệ
- Thiết lập kỹ năng và thành tích
- Tạo giao diện quiz nâng cao
- Xây dựng phân tích riêng cho luyện tập
- Tối ưu hóa hiệu suất gaming

**Kết quả**:
- Dịch vụ devgame hoàn chỉnh
- Tính năng gamification
- Hệ thống tiền tệ và kỹ năng
- Trải nghiệm người dùng nâng cao

### Giai đoạn 5: Tích hợp & Kiểm thử (Tuần 8)
**Mục tiêu**: Tích hợp end-to-end và kiểm thử

**Nhiệm vụ**:
- Kiểm thử luồng người dùng hoàn chỉnh
- Tối ưu hóa hiệu suất
- Kiểm tra tải với người dùng đồng thời
- Kiểm thử bảo mật
- Hoàn thiện tài liệu
- Tạo tài liệu đào tạo

**Kết quả**:
- Hệ thống tích hợp đầy đủ
- Kết quả kiểm tra hiệu suất
- Báo cáo kiểm toán bảo mật
- Tài liệu hoàn chỉnh
- Tài liệu đào tạo

### Giai đoạn 6: Triển khai & Giám sát (Tuần 9)
**Mục tiêu**: Triển khai production và thiết lập giám sát

**Nhiệm vụ**:
- Thiết lập môi trường production
- Cấu hình giám sát và cảnh báo
- Quy trình sao lưu và khôi phục
- Giám sát hiệu suất
- Kiểm thử chấp nhận người dùng
- Chuẩn bị go-live

**Kết quả**:
- Triển khai production
- Dashboard giám sát
- Quy trình vận hành
- Baseline hiệu suất

## 🔒 Cân nhắc Bảo mật

### Xác thực Liên dịch vụ
- Sử dụng token bí mật để xác thực giữa các dịch vụ
- Mã hóa tất cả giao tiếp inter-service
- Kiểm soát truy cập dựa trên vai trò
- Ghi log kiểm toán toàn diện

### Bảo mật Phiên
- Session store dựa trên Redis với TTL phù hợp
- Cookie bảo mật với httpOnly và sameSite
- Mã hóa dữ liệu nhạy cảm
- Xác thực chéo dịch vụ

### Bảo vệ Dữ liệu
- Mã hóa tất cả dữ liệu nhạy cảm khi lưu trữ
- TLS cho tất cả giao tiếp mạng
- Kiểm soát truy cập dựa trên vai trò
- Xác thực đầu vào nghiêm ngặt tại ranh giới dịch vụ

## 📈 Giám sát & Quan sát

### Triển khai Kiểm tra Sức khỏe
Mỗi dịch vụ sẽ có endpoint `/health` để kiểm tra:
- Kết nối cơ sở dữ liệu
- Kết nối Redis
- Sức khỏe dịch vụ phụ thuộc
- Sử dụng bộ nhớ và CPU
- Thời gian hoạt động dịch vụ

### Thu thập Metrics
- Thời gian phản hồi HTTP
- Số lượng phiên quiz đang hoạt động
- Sử dụng tài nguyên hệ thống
- Tỷ lệ lỗi theo dịch vụ
- Throughput cơ sở dữ liệu

### Cảnh báo và Thông báo
- Cảnh báo khi dịch vụ không khỏe mạnh
- Thông báo khi sử dụng tài nguyên cao
- Cảnh báo khi thời gian phản hồi chậm
- Thông báo khi có lỗi hệ thống

## 🧪 Chiến lược Kiểm thử

### Kiểm thử Đơn vị
- Kiểm thử logic nghiệp vụ của từng dịch vụ
- Kiểm thử các service layer và controller
- Mock dependencies bên ngoài
- Coverage tối thiểu 80%

### Kiểm thử Tích hợp
- Kiểm thử luồng người dùng end-to-end
- Kiểm thử giao tiếp inter-service
- Kiểm thử đồng bộ hóa cơ sở dữ liệu
- Kiểm thử session management

### Kiểm thử Tải
- Kiểm thử với 300+ người dùng đồng thời
- Kiểm thử hiệu suất từng dịch vụ
- Kiểm thử khả năng phục hồi
- Kiểm thử memory leaks

## 🚨 Đánh giá Rủi ro & Giảm thiểu

### Rủi ro Kỹ thuật
| Rủi ro | Tác động | Xác suất | Giảm thiểu |
|--------|----------|----------|------------|
| Lỗi Giao tiếp Dịch vụ | Cao | Trung bình | Circuit breaker, fallback mechanisms |
| Cạn kiệt Connection Pool DB | Cao | Thấp | Tối ưu connection pooling, giám sát |
| Memory Leaks trong Microservices | Trung bình | Trung bình | Giám sát thường xuyên, restart tự động |
| Vấn đề Đồng bộ Session | Trung bình | Thấp | Redis shared sessions, TTL phù hợp |
| Network Latency giữa Services | Thấp | Cao | Triển khai local, connection pooling |

### Rủi ro Kinh doanh
| Rủi ro | Tác động | Xác suất | Giảm thiểu |
|--------|----------|----------|------------|
| Giảm Trải nghiệm Người dùng | Cao | Thấp | Kiểm thử kỹ lưỡng, rollout từ từ |
| Mất Dữ liệu khi Migration | Cao | Thấp | Chiến lược backup toàn diện |
| Downtime Kéo dài | Trung bình | Thấp | Blue-green deployment, rollback plan |
| Đường cong Học tập của Team | Trung bình | Cao | Chương trình đào tạo, tài liệu |

## 💰 Phân tích Chi phí-Lợi ích

### Chi phí Phát triển
- **Thời gian Phát triển**: 9 tuần (1 senior developer + 1 junior developer)
- **Cơ sở hạ tầng**: Không có chi phí bổ sung (cùng server)
- **Kiểm thử**: 1 tuần dành riêng cho kiểm thử
- **Đào tạo**: 2 ngày đào tạo team

### Lợi ích
- **Hiệu suất**: Cải thiện 30-40% thời gian phản hồi
- **Hiệu quả Tài nguyên**: Tiết kiệm 1GB RAM, 25% hiệu quả CPU
- **Khả năng Mở rộng**: Sẵn sàng cho horizontal scaling
- **Bảo trì**: Giảm độ phức tạp, debug dễ hơn
- **Tương lai**: Nền tảng kiến trúc microservice

### Tính toán ROI
- **Ngắn hạn**: Cải thiện trải nghiệm người dùng, giảm chi phí server
- **Trung hạn**: Phát triển tính năng dễ hơn, chu kỳ deployment nhanh hơn
- **Dài hạn**: Khả năng horizontal scaling, giảm technical debt

## 📋 Metrics Thành công

### Metrics Hiệu suất
- **Thời gian Phản hồi**: <200ms cho tải quiz (mục tiêu: cải thiện 50%)
- **Sử dụng Bộ nhớ**: <6GB tổng RAM (mục tiêu: giảm 1GB)
- **Người dùng Đồng thời**: Hỗ trợ 300+ người làm quiz đồng thời (mục tiêu: tăng 50%)
- **Uptime Dịch vụ**: 99.9% availability mỗi dịch vụ

### Metrics Trải nghiệm Người dùng
- **Thời gian Tải Quiz**: <3 giây (mục tiêu: cải thiện 2 giây)
- **Độ Mượt Điều hướng**: Zero failed redirections
- **Tính khả dụng Tính năng**: 100% feature parity trong các chế độ tương ứng
- **Tỷ lệ Lỗi**: <0.1% lỗi người dùng gặp phải

### Metrics Kinh doanh
- **Tốc độ Phát triển**: Phát triển tính năng nhanh hơn 30%
- **Giải quyết Bug**: Debug và fix nhanh hơn 50%
- **Tần suất Deployment**: Deployment hàng tuần mỗi dịch vụ
- **Độ tin cậy Hệ thống**: 99.9% uptime với khôi phục tự động

## 🎯 Kết luận & Khuyến nghị

### Tóm tắt Điều hành
Kiến trúc microservice đa nhánh được đề xuất đại diện cho sự phát triển chiến lược của hệ thống QL_CTDT, giải quyết các hạn chế tài nguyên hiện tại đồng thời định vị nền tảng cho sự phát triển tương lai. Thiết kế hub-and-spoke với các dịch vụ chuyên biệt mang lại cải thiện hiệu suất đáng kể và lợi ích vận hành.

### Khuyến nghị Chính
1. **Phê duyệt Triển khai**: Tiến hành với lộ trình triển khai 9 tuần
2. **Phân bổ Tài nguyên**: Giao team phát triển chuyên dụng cho dự án
3. **Thiết lập Giám sát**: Triển khai giám sát toàn diện từ ngày đầu
4. **Lập kế hoạch Đào tạo**: Lên lịch các buổi đào tạo team trong quá trình phát triển
5. **Chuẩn bị Rollback**: Duy trì hệ thống hiện tại làm fallback trong quá trình chuyển đổi

### Các Bước Tiếp theo
1. **Tuần 1**: Kickoff team và thiết lập môi trường
2. **Tuần 2**: Bắt đầu triển khai Giai đoạn 1
3. **Tuần 4**: Review milestone đầu tiên và điều chỉnh
4. **Tuần 6**: Đánh giá giữa dự án và tối ưu hóa
5. **Tuần 8**: Kiểm thử trước triển khai và validation
6. **Tuần 9**: Triển khai production và giám sát

### Tầm nhìn Dài hạn
Kiến trúc này thiết lập nền tảng cho:
- **Horizontal Scaling**: Scaling từng dịch vụ dựa trên nhu cầu
- **Độc lập Tính năng**: Phát triển song song các tính năng đánh giá và luyện tập
- **Tiến hóa Công nghệ**: Áp dụng từ từ công nghệ mới cho từng dịch vụ
- **Chuyên biệt hóa Team**: Team chuyên dụng cho các domain dịch vụ khác nhau

---

**Phiên bản Tài liệu**: 1.0  
**Được chuẩn bị bởi**: Team Phát triển  
**Ngày Review**: 15/01/2025  
**Cần Phê duyệt**: Technical Lead, Project Manager, Stakeholders  
**Bắt đầu Triển khai**: Sau khi được phê duyệt
